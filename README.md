# Nuxt Starter Pro
$env:DEBUG='*'; pnpm dev - 最详细的终端信息输出
"dev": "DEBUG=vite:transform,tailwindcss nuxi dev"
或者 nuxt.config.ts 中 debug: true,
hmr 的慢主要是 tailwindcss 慢，可是为什么修改页面字符也需要重新渲染 tailwindcss 呢？

## Features

- [x] Nuxt 3
- [x] Tailwind CSS v4
- [x] Shadcn Vue 2.0.1

pnpm dlx shadcn-vue@2.0.1 add accordion alert alert-dialog aspect-ratio avatar badge breadcrumb button calendar card
pnpm dlx shadcn-vue@2.0.1 add checkbox collapsible command context-menu
pnpm dlx shadcn-vue@2.0.1 add dialog drawer dropdown-menu form hover-card input label menubar navigation-menu
pnpm dlx shadcn-vue@2.0.1 add pagination pin-input popover progress radio-group range-calendar resizable scroll-area select separator sheet sidebar skeleton slider sonner switch table tabs tags-input textarea toggle toggle-group tooltip
pnpm dlx shadcn-vue@2.0.1 add chart chart-area chart-bar chart-donut chart-line 