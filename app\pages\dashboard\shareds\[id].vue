<script setup lang="ts">
import { useRoute } from 'vue-router';
import { refDebounced } from '@vueuse/core'
import { onClickOutside } from '@vueuse/core'
import ListItem from '@/components/bookmark/ListItem.vue'
import FolderBreadcrumbs from '@/components/bookmark/FolderBreadcrumbs.vue'
import ViewOptions from '@/components/bookmark/ViewOptions.vue'
import SortingOptions from '@/components/bookmark/SortingOptions.vue'
import { type BookmarkTreeNode, countBookmarksAndFoldersById, getFlattenedSubTree, sortBookmarks, get, getChildren } from '~/utils/bookmark'
import { select, selectAll } from '~/utils/multiselect'

/*
 * 共享书签页面业务执行逻辑说明
 * =====================================
 *
 * 页面初始化流程：
 * 1. 从 URL 参数获取 sharedId
 * 2. 通过 useFetch 调用 API 获取共享数据 (shared)
 * 3. 解析 shared.bookmarkData (JSON字符串 -> 对象数组)
 * 4. 初始化当前文件夹ID (currentFolderId)：
 *    - 优先使用 URL query 中的 foldid 参数
 *    - 否则调用 getFirstFolderId() 获取第一个文件夹ID
 * 5. 调用 get() 获取当前文件夹信息 (currentFolder)
 * 6. 如果 URL 中有 bookmarkId，设置为选中状态
 *
 * 书签列表更新流程 (updateBookmarkList)：
 * 1. 调用 refreshBookmarksData()：
 *    - getChildren() 获取当前层级书签 (currentLevelBookmarks)
 *    - getFlattenedSubTree() 获取包含子文件夹的所有书签 (includedSubFoldersBookmarks)
 *    - countBookmarksAndFoldersById() 统计文件夹和书签数量
 * 2. 根据搜索状态选择数据源：
 *    - 搜索时且包含子文件夹：使用 includedSubFoldersBookmarks
 *    - 否则：使用 currentLevelBookmarks
 * 3. 应用过滤器：
 *    - filterByTags() 按标签过滤 (Web版本暂不支持)
 *    - filterByKeyword() 按关键词过滤
 * 4. 调用 renderInBatches() 分批渲染，提高性能
 *
 * 文件夹导航流程 (navigateToFolder)：
 * 1. 更新 currentFolderId
 * 2. 调用 get() 获取新文件夹信息
 * 3. 更新 currentFolder
 * 4. 清空选中状态
 * 5. 触发 watch 监听器，自动调用 updateBookmarkList()
 *
 * 触发器和监听器：
 * - watch([currentFolderId, searchDebounced, selectedTags, includeSubfolders])
 *   监听这些变量变化，自动调用 updateBookmarkList()
 * - watch([bookmarkData, route.query.foldid])
 *   监听数据加载和URL参数变化，初始化文件夹状态
 */

definePageMeta({
  title: 'Shareds',
  layout: 'dashboard',
  middleware: ['auth'],
})

const route = useRoute();

// dialogs
const editBookmarkDialogOpen = ref(false)
const editFolderDialogOpen = ref(false)
const bulkAddTagDialogOpen = ref(false)
const bulkRemoveTagDialogOpen = ref(false)
const bulkNoteDialogOpen = ref(false)
const bulkEmojiDialogOpen = ref(false)
const confirmDeleteDialogOpen = ref(false)

// 从 URL 参数中获取 shared ID
const sharedId = route.params.id as string

const {
  data: shared,
  error,
} = await useFetch('/api/dashboard/shareds/' + sharedId)

// 检查是否获取到 shared 数据
if (error.value || !shared.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Shared not found'
  })
}

// 使用类型断言
const bookmarkData = computed(() => {
  try {
    const data = (shared.value as any)?.bookmarkData
    return typeof data === 'string' ? JSON.parse(data) : data || []
  } catch (e) {
    console.error('解析书签数据失败:', e)
    return []
  }
})

// 获取第一个文件夹的 ID
const getFirstFolderId = async (bookmarks: any[]) => {
  if (!bookmarks || bookmarks.length === 0) return '1'

  // 查找第一个文件夹（没有 url 属性的节点）
  const findFirstFolder = (nodes: any[]): string | null => {
    for (const node of nodes) {
      if (!node.url && node.children) {
        return node.id
      }
    }
    return null
  }

  const firstFolderId = findFirstFolder(bookmarks)
  return firstFolderId || '1'
}

const currentFolderId = ref<string>('')
const currentFolder = ref();

// 初始化 currentFolderId
watch(
  [bookmarkData, () => route.query.foldid],
  async ([newBookmarkData, foldid]) => {
    if (!newBookmarkData || newBookmarkData.length === 0) return

    // 如果 URL 中有 foldid 参数，使用它；否则使用第一个文件夹的 id
    if (foldid) {
      currentFolderId.value = foldid as string
    } else {
      currentFolderId.value = await getFirstFolderId(newBookmarkData)
    }

    // 获取当前文件夹信息
    const folderResult = await get(newBookmarkData, currentFolderId.value)
    currentFolder.value = folderResult[0]

    if (route.query.bookmarkId) {
      selectedIds.value = [route.query.bookmarkId as string]
    }
  },
  { immediate: true }
);

const selectedTags = ref<string[]>([]);
const search = ref<string>('')
const searchDebounced = refDebounced(search, 35)
const includeSubfolders = ref<boolean | 'indeterminate'>(true)

// 实际在页面上渲染的书签列表(包括深层搜索结果或者仅是当前文件夹书签)，分批加载，以提高渲染性能
const listBookmarks = ref<BookmarkTreeNode[]>([])
const currentLevelBookmarks = ref<BookmarkTreeNode[]>([])
const includedSubFoldersBookmarks = ref<BookmarkTreeNode[]>([])
const subFolderCount = ref(0)
const subBookmarkCount = ref(0)
async function refreshBookmarksData() {
  currentLevelBookmarks.value = await getChildren(bookmarkData.value, currentFolderId.value)
  includedSubFoldersBookmarks.value = await getFlattenedSubTree(bookmarkData.value, currentFolderId.value)
  const { folderCount, bookmarkCount } = await countBookmarksAndFoldersById(bookmarkData.value, currentFolderId.value);
  subFolderCount.value = folderCount
  subBookmarkCount.value = bookmarkCount
}

// 多选
const pivotId = ref<string | null>(null)
const selectedIds = ref<string[]>([])
const selectedItem = computed(() => {
  return listBookmarks.value.find(item => item.id === selectedIds.value[0])
})

// 单击时选中一个书签，如果按住 ctrl 或 meta 键，则为多选
// 如果按住 shift 键，则选中从 pivotId 到 itemId 之间的所有书签
function handleClick(itemId: string, e?: PointerEvent) {
  if (e?.ctrlKey || e?.metaKey) {
    const [pivot, newIds] = select({
      selectedIds: selectedIds.value,
      selectedId: pivotId.value!,
      id: itemId
    })
    pivotId.value = pivot
    selectedIds.value = newIds
  } else if (e?.shiftKey) {
    const selected = selectAll({
      selectedIds: selectedIds.value,
      selectedId: pivotId.value!,
      ids: listBookmarks.value.map(item => item.id),
      id: itemId
    })
    selectedIds.value = selected
  } else {
    if (selectedIds.value.includes(itemId) && selectedIds.value.length === 1) {
      pivotId.value = null
      selectedIds.value = []
    } else {
      pivotId.value = itemId
      selectedIds.value = [itemId]
    }
  }
}

// 双击打开书签或者跳转文件夹
async function handleDblclick(item: BookmarkTreeNode) {
  if (item.url) {
    window.open(item.url, '_blank');
    selectedIds.value = [item.id]
  } else {
    await navigateToFolder(item.id)
  }
}

// 导航到指定文件夹
async function navigateToFolder(folderId: string) {
  // 直接更新当前文件夹ID，而不是跳转URL
  currentFolderId.value = folderId

  // 获取当前文件夹信息
  const folderResult = await get(bookmarkData.value, currentFolderId.value)
  currentFolder.value = folderResult[0]

  // 清空选中状态
  selectedIds.value = []
  pivotId.value = null
}

// 实现批量打开书签功能，循环打开 selectedIds 中的书签
// type 分别为普通打开，在新窗口中打开，以及在无痕式窗口中打开
function bulkOpenBookmark(type: 'tab' | 'window' | 'incognito') {
  const urls = selectedIds.value
    .map(id => listBookmarks.value.find(item => item.id === id)?.url)
    .filter(Boolean) as string[];
  if (urls.length === 0) return;
  
  if (type === 'tab') {
    // 普通打开，每个URL都在新标签页打开
    urls.forEach(url => window.open(url, '_blank'));
  } else {
    // 在新窗口中打开，由于Web应用无法控制窗口状态，所以只能简单打开第一个URL
    // 无痕模式在Web中无法实现，所以与window模式相同处理
    if (urls.length > 0) {
      window.open(urls[0], '_blank');
    }
  }
}

// 添加专门的右键处理函数
function handleRightClick(e: MouseEvent, itemId: string) {  
  // 如果当前右键项不在选中列表中
  if (!selectedIds.value.includes(itemId)) {
    selectedIds.value = [itemId]
    pivotId.value = itemId
  }
}


function isSelected(id: string) {
  return selectedIds.value.includes(id)
}

const multiSelectTarget = ref<HTMLElement | null>(null)
const contextMenuOpen = ref(false);
onClickOutside(multiSelectTarget, () => {
  if (
    !contextMenuOpen.value && 
    !editFolderDialogOpen.value && 
    !editBookmarkDialogOpen.value && 
    !confirmDeleteDialogOpen.value &&
    !bulkAddTagDialogOpen.value &&
    !bulkRemoveTagDialogOpen.value &&
    !bulkNoteDialogOpen.value &&
    !bulkEmojiDialogOpen.value
  ) {
    selectedIds.value = []
    pivotId.value = null
  }
}, {ignore: []});

function itemDragStart(itemId: string) {
  if (!selectedIds.value.includes(itemId)) {
    selectedIds.value = [itemId]
  }
}

// 确认删除
const handleDeleteSuccess = () => {
  selectedIds.value = [];
};

// 分批渲染函数
const animationFrameId = ref<number | null>(null);
async function renderInBatches(items: BookmarkTreeNode[]) {
  const start = performance.now();
  // 取消之前的动画帧
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value);
    animationFrameId.value = null;
  }

  const sortedBookmarks = sortBookmarks(items, sortBy.value)
  let index = 0;
  const batchSize = 15;
  let currentBatch: BookmarkTreeNode[] = [];
  // 清空当前列表 - 会导致闪烁
  listBookmarks.value = [];

  function processBatch() {
    const end = Math.min(index + batchSize, sortedBookmarks.length);
    for (; index < end; index++) {
      if (sortedBookmarks[index]) {
        currentBatch.push(sortedBookmarks[index] as BookmarkTreeNode);
      }
    }
    
    listBookmarks.value = [...currentBatch];
    
    if (index < sortedBookmarks.length) {
      animationFrameId.value = requestAnimationFrame(processBatch);
    } else {
      const processEnd = performance.now();
      console.log(`All items processed took ${(processEnd - start).toFixed(0)} milliseconds`);
      animationFrameId.value = null;
    }
  }

  processBatch();
}

// 可选的排序方式
const sortBy = ref<{ field: 'default' | 'title' | 'dateAdded' | 'url' | 'id' | 'dateLastUsed'; direction: 'asc' | 'desc' }>({
  field: 'default',
  direction: 'asc'
});

// 修改排序
function handleSort(field: 'default' | 'title' | 'dateAdded' | 'url' | 'id') {
  if (sortBy.value.field === field) {
    // 切换排序方向
    sortBy.value.direction = sortBy.value.direction === 'asc' ? 'desc' : 'asc';
  } else {
    // 切换排序字段并重置方向
    sortBy.value.field = field;
    sortBy.value.direction = 'asc';
  }
  renderInBatches(listBookmarks.value); // 重新加载数据应用排序
}

// 存储标签和额外信息
const extras = ref<any[]>([])
const uniqueTags = ref<any[]>([]);

// 当前是否是搜索状态，或者有标签选中
const isFiltered = computed(() => search.value.length > 0 || selectedTags.value.length > 0)

// 重置搜索
function resetSearch() {
  search.value = ''
  selectedTags.value = []
}

// 根据标签过滤书签
async function filterByTags(bookmarks: BookmarkTreeNode[]) {
  if (selectedTags.value.length === 0) return bookmarks;
  // Web版本不支持标签过滤，直接返回原始书签
  return bookmarks;
}

// 根据关键词过滤书签，如果是搜索，最多返回 100 个结果
async function filterByKeyword(bookmarks: BookmarkTreeNode[]) {
  if (!searchDebounced.value) return bookmarks;
  const lowerTerm = searchDebounced.value.toLowerCase();
  const filtered = bookmarks.filter(item => {
    const title = item.title?.toLowerCase() || '';
    const url = item.url?.toLowerCase() || '';
    return title.includes(lowerTerm) || url.includes(lowerTerm);
  });
  return filtered.slice(0, 100);
}

// 更新书签列表
async function updateBookmarkList() {
  const start = performance.now();
  await refreshBookmarksData()
  let allBookmarks: BookmarkTreeNode[] = []
  // 非搜索时仅显示当前层级书签，但搜索时可能要搜索深层书签
  if (isFiltered.value && includeSubfolders.value) {
    allBookmarks = includedSubFoldersBookmarks.value
  } else {
    allBookmarks = currentLevelBookmarks.value
  }
  
  const filteredByTags = await filterByTags(allBookmarks);
  const filteredByKeyword = await filterByKeyword(filteredByTags);
  renderInBatches(filteredByKeyword);
  const end = performance.now();
  console.warn('--------- updateBookmarkList -----------', (end - start).toFixed(0), 'ms')
}

watch([currentFolderId, searchDebounced, selectedTags, includeSubfolders], updateBookmarkList, { immediate: true });

onMounted(async () => {
  // Web版本不需要特殊初始化
});

onUnmounted(() => {
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value);
  }
});
</script>

<template>
  <div>
    <div class="flex justify-between mt-4 items-center">
      <div class="flex flex-col space-y-1 items-start">
        <FolderBreadcrumbs
          v-if="bookmarkData && bookmarkData.length > 0 && currentFolderId"
          :bookmarkData="bookmarkData"
          :folderId="currentFolderId"
          :maxCrumbs="5"
          @navigate="navigateToFolder"
        />
        <Tooltip>
          <TooltipTrigger as-child>
            <h2 class="text-2xl font-bold">{{ currentFolder?.title }}</h2>
          </TooltipTrigger>
          <TooltipContent>
            <p class="text-sm">
              <span class="font-bold">{{ subFolderCount }}</span> sub folders,
              <span class="font-bold">{{ subBookmarkCount }}</span> bookmarks,
              <span class="font-bold">{{ uniqueTags.length }}</span> tags.
            </p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>

    shared: {{ bookmarkData.length }}
    
    <div class="flex items-center justify-between mt-4">
      <div class="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Filter bookmarks ..."
          v-model="search"
          class="h-10 w-[220px] lg:w-[350px] rounded-full pl-4 bg-secondary pt-0 focus-visible:ring-2"
        />
        <div class="flex items-center space-x-2">
          <Checkbox 
            id="includeSubfolders" 
            v-model="includeSubfolders"
            class="h-4 w-4"
          />
          <label 
            for="includeSubfolders" 
            class="text-sm font-semibold leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Sub folders
          </label>
        </div>
        <Button
          v-if="isFiltered"
          variant="ghost"
          class="h-8 px-2 lg:px-3"
          @click="resetSearch()"
        >
          Reset
          <Icon name="radix-icons:cross-2" />
        </Button>
      </div>
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          class="h-8 px-2 lg:px-3"
        >
          <Icon name="lucide:share" /> Share
        </Button>
        <SortingOptions 
          :sort-by="sortBy"
          @sort="handleSort"
        />
        <ViewOptions />
      </div>
    </div>

    <div v-if="listBookmarks.length" class="rounded-md border mt-4 py-2" ref="multiSelectTarget">
      <ContextMenu 
          v-model:open="contextMenuOpen"
          @open-change="(open: boolean) => contextMenuOpen = open"
        >
          <ContextMenuTrigger>
          <ul>
            <ListItem
              v-for="item in listBookmarks"
              :key="item.id"
              :data-state="isSelected(item.id) && 'selected'"
              @contextmenu="(e: MouseEvent) => handleRightClick(e, item.id)"
              @click.stop="(e: PointerEvent) => handleClick(item.id, e)"
              @dblclick="handleDblclick(item)"
              class="transition-none data-[state=selected]:bg-muted"
              :item="item"
              :selectedIds="selectedIds"
              :tags="uniqueTags.filter(tag => tag.bookmarkIds.includes(item.id))"
              :extra="extras.find(extra => extra.bookmarkId === item.id)"
              @drag-start="itemDragStart"
              @open-context-menu="contextMenuOpen = true"
              @delete="(id) => { handleClick(id); confirmDeleteDialogOpen = true }"
            >
            </ListItem>
          </ul>
          </ContextMenuTrigger>
          <ContextMenuContent 
            class="v-context-menu-content"
          >
            <ContextMenuItem 
              @select="confirmDeleteDialogOpen = true"
            >
              'context_menu.deleteWithCount' {{ selectedIds.length }}
            </ContextMenuItem>
            <ContextMenuSeparator />
            <ContextMenuSub>
              <ContextMenuSubTrigger>
                {{ 'context_menu.bulkActions' }}
              </ContextMenuSubTrigger>
              <ContextMenuSubContent>
                <ContextMenuItem 
                  @select="bulkAddTagDialogOpen = true"
                >
                  {{ 'context_menu.bulkAddTag' }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkRemoveTagDialogOpen = true"
                >
                  {{ 'context_menu.bulkRemoveTag' }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkNoteDialogOpen = true"
                >
                  {{ 'context_menu.bulkNote' }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkEmojiDialogOpen = true"
                >
                  {{ 'context_menu.bulkEmoji' }}
                </ContextMenuItem>
              </ContextMenuSubContent>
            </ContextMenuSub>
            <ContextMenuSeparator />
            <ContextMenuItem 
              @select="bulkOpenBookmark('tab')"
            >
              'context_menu.openAll' {{ [selectedIds.length] }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="bulkOpenBookmark('window')"
            >
              'context_menu.openInNewWindow' {{ [selectedIds.length] }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="bulkOpenBookmark('incognito')"
            >
             'context_menu.openIncognito' {{ [selectedIds.length] }}
            </ContextMenuItem>
          </ContextMenuContent>
        </ContextMenu>
    </div>
    <div v-else class="flex flex-col h-48 text-center rounded-md border mt-4 justify-center gap-2">
      <h3 class="text-lg font-semibold">
        {{ 'options.list.noResults' }}
      </h3>
      <p class="text-sm text-muted-foreground">
        {{ 'options.list.noResultsDescription' }}
      </p>
    </div>
  </div>
</template>
