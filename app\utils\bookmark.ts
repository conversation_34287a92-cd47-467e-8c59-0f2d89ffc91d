// 定义书签节点类型，与Chrome API类型兼容
export interface BookmarkTreeNode {
  id: string;
  parentId?: string;
  index?: number;
  url?: string;
  title?: string;
  dateAdded?: number;
  dateGroupModified?: number;
  dateLastUsed?: number;
  children?: BookmarkTreeNode[];
  syncing?: boolean;
}

// 通用函数，用于扁平化书签树形结构
export function flattenBookmarks(bookmarks: BookmarkTreeNode[]) {
  return bookmarks.reduce((acc: BookmarkTreeNode[], item: BookmarkTreeNode) => {
    acc.push(item);
    if (item.children) {
      acc.push(...flattenBookmarks(item.children));
    }
    return acc;
  }, []);
}

// 递归查找父级 ids 的方法
export function getAllParentIds(bookmarks: BookmarkTreeNode[], folderId: string) {
  const parentIds: string[] = [];
  
  function findParent(nodes: BookmarkTreeNode[], id: string): boolean {
    for (const node of nodes) {
      if (node.id === id) {
        if (node.parentId) {
          parentIds.push(node.parentId);
          findParent(bookmarks, node.parentId);
        }
        return true;
      }
      if (node.children && node.children.length > 0) {
        if (findParent(node.children, id)) {
          return true;
        }
      }
    }
    return false;
  }
  
  findParent(bookmarks, folderId);
  return Promise.resolve(parentIds);
}

// 在已加载到内存中的书签树结构中递归查找父级路径 ids
export function findParentPath(
  nodes: BookmarkTreeNode[],
  targetId: string,
  path: string[] = []
): string[] {
  for (const node of nodes) {
    if (node.id === targetId) {
      return [...path, node.id];
    }
    if (node.children) {
      const result = findParentPath(node.children, targetId, [...path, node.id]);
      if (result.length > 0) {
        return result;
      }
    }
  }
  return [];
}

// 递归计算子节点的所有文件夹数和 bookmarks 数
export function countBookmarksAndFoldersById(bookmarks: BookmarkTreeNode[], currentFolderId: string) {
  const start = performance.now();
  let folderCount = 0;
  let bookmarkCount = 0;

  function traverse(node: BookmarkTreeNode) {
    // 如果当前节点有 url 属性，那么它是一个书签
    if (node.url) {
      bookmarkCount++;
    } else if (node.children && node.children.length > 0) {
      // 如果当前节点有 children 属性且不为空，那么它是一个文件夹
      folderCount++;
      node.children.forEach(traverse); // 递归遍历子节点
    }
  }

  function findAndCount(node: BookmarkTreeNode) {
    if (node.id === currentFolderId) {
      traverse(node); // 找到目标节点，开始遍历其子节点
    } else if (node.children) {
      // 遍历当前节点的子节点
      node.children.forEach(findAndCount);
    }
  }

  // 从根节点开始查找目标节点及其子树
  bookmarks.forEach(findAndCount);
  const end = performance.now();
  console.log(`folderCount took ${(end - start).toFixed(0)} milliseconds`);
  return Promise.resolve({ folderCount, bookmarkCount });
}

// 递归获取子树
export function getSubTree(bookmarks: BookmarkTreeNode[], folderId: string) {
  const result: BookmarkTreeNode[] = [];
  
  function findNode(nodes: BookmarkTreeNode[], id: string): boolean {
    for (const node of nodes) {
      if (node.id === id) {
        result.push(JSON.parse(JSON.stringify(node)));
        return true;
      }
      if (node.children && node.children.length > 0) {
        if (findNode(node.children, id)) {
          return true;
        }
      }
    }
    return false;
  }
  
  bookmarks.forEach(node => findNode(bookmarks, folderId));
  return Promise.resolve(result);
}

// 递归获取平铺书签的函数
export function getFlattenedSubTree(bookmarks: BookmarkTreeNode[], folderId: string) {
  const start = performance.now();
  
  const subTree = getSubTree(bookmarks, folderId);
  
  const flatten = (nodes: BookmarkTreeNode[]): BookmarkTreeNode[] => {
    return nodes.flatMap(node => {
      const children = node.children ? flatten(node.children) : [];
      return [node, ...children];
    });
  };
  
  const result = subTree.then(tree => flatten(tree));
  
  const end = performance.now();
  console.log(`getFlattenedSubTree took ${(end - start).toFixed(0)} milliseconds`);
  return result;
}

// 递归获取所有书签ID（包含子文件夹）
export function getAllBookmarkIds(bookmarks: BookmarkTreeNode[], folderId: string): Promise<string[]> {
  return getSubTree(bookmarks, folderId).then(nodes => {
    const bookmarkIds: string[] = [];

    function traverse(node: BookmarkTreeNode) {
      if (node.url) { // 书签节点
        bookmarkIds.push(node.id);
      } else if (node.children) { // 文件夹节点
        node.children.forEach(traverse);
      }
    }

    nodes.forEach(traverse);
    return bookmarkIds;
  });
}

// 获取指定ID的书签
export function getBookmark(bookmarks: BookmarkTreeNode[], id: string): Promise<BookmarkTreeNode[]> {
  const result: BookmarkTreeNode[] = [];
  
  function findNode(nodes: BookmarkTreeNode[], targetId: string): boolean {
    for (const node of nodes) {
      if (node.id === targetId) {
        result.push(JSON.parse(JSON.stringify(node)));
        return true;
      }
      if (node.children && node.children.length > 0) {
        if (findNode(node.children, targetId)) {
          return true;
        }
      }
    }
    return false;
  }
  
  bookmarks.forEach(node => findNode([node], id));
  return Promise.resolve(result);
}

// 书签排序函数
export function sortBookmarks(
  items: BookmarkTreeNode[],
  sortBy: { field: 'default' | 'title' | 'dateAdded' | 'url' | 'id' | 'dateLastUsed'; direction: 'asc' | 'desc' }
): BookmarkTreeNode[] {
  // 仅在非默认排序时进行排序
  if (!sortBy.field) return [...items];

  return [...items].sort((a, b) => {
    let compareResult = 0;
    
    switch (sortBy.field) {
      case 'default':
        compareResult = (a.index || 0) - (b.index || 0);
        break;
      case 'id':
        const numA = parseInt(a.id, 10);
        const numB = parseInt(b.id, 10);
        compareResult = (numA - numB) || a.id.localeCompare(b.id);
        break;
      case 'title':
        compareResult = (a.title || '').localeCompare(b.title || '');
        if (compareResult === 0) {
          const numA = parseInt(a.id, 10);
          const numB = parseInt(b.id, 10);
          compareResult = (numA - numB) || a.id.localeCompare(b.id);
        }
        break;
      case 'dateAdded':
        compareResult = (a.dateAdded || 0) - (b.dateAdded || 0);
        if (compareResult === 0) {
          const numA = parseInt(a.id, 10);
          const numB = parseInt(b.id, 10);
          compareResult = (numA - numB) || a.id.localeCompare(b.id);
        }
        break;
      case 'dateLastUsed':
        compareResult = (a.dateLastUsed || 0) - (b.dateLastUsed || 0);
        if (compareResult === 0) {
          const numA = parseInt(a.id, 10);
          const numB = parseInt(b.id, 10);
          compareResult = (numA - numB) || a.id.localeCompare(b.id);
        }
        break;
      case 'url':
        const normalizeUrl = (url?: string) => url?.replace(/^https?:\/\//i, '').toLowerCase() || '';
        compareResult = normalizeUrl(a.url).localeCompare(normalizeUrl(b.url));
        if (compareResult === 0) {
          const numA = parseInt(a.id, 10);
          const numB = parseInt(b.id, 10);
          compareResult = (numA - numB) || a.id.localeCompare(b.id);
        }
        break;
    }
    
    return sortBy.direction === 'asc' ? compareResult : -compareResult;
  });
}

/**
 * 获取指定ID的书签节点，类似chrome.bookmarks.get()
 * @param bookmarks 书签数据
 * @param id 书签ID或ID数组
 * @returns Promise<BookmarkTreeNode[]> 书签节点数组
 */
export function get(bookmarks: BookmarkTreeNode[], id: string | string[]): Promise<BookmarkTreeNode[]> {
  const ids = Array.isArray(id) ? id : [id];
  const result: BookmarkTreeNode[] = [];
  
  function findBookmark(nodes: BookmarkTreeNode[], targetId: string): boolean {
    for (const node of nodes) {
      if (node.id === targetId) {
        // 创建副本以避免修改原始数据
        result.push(JSON.parse(JSON.stringify(node)));
        return true;
      }
      if (node.children && node.children.length > 0) {
        if (findBookmark(node.children, targetId)) {
          return true;
        }
      }
    }
    return false;
  }
  
  // 查找每个请求的ID
  for (const bookmarkId of ids) {
    // 跳过空字符串或无效ID
    if (!bookmarkId || bookmarkId.trim() === '') {
      continue;
    }

    let found = false;
    for (const rootNode of bookmarks) {
      if (findBookmark([rootNode], bookmarkId)) {
        found = true;
        break;
      }
    }

    if (!found) {
      console.warn(`未找到ID为 ${bookmarkId} 的书签`, new Error().stack);
    }
  }
  
  return Promise.resolve(result);
}

/**
 * 获取指定ID的书签节点的子节点，类似chrome.bookmarks.getChildren()
 * @param bookmarks 书签数据
 * @param id 父书签ID
 * @returns Promise<BookmarkTreeNode[]> 子书签节点数组
 */
export function getChildren(bookmarks: BookmarkTreeNode[], id: string): Promise<BookmarkTreeNode[]> {
  // 先找到指定ID的节点
  return get(bookmarks, id).then(nodes => {
    if (nodes.length === 0) {
      return [];
    }
    
    const parentNode = nodes[0];
    
    // 如果节点没有children属性或children为空，返回空数组
    if (!parentNode?.children || parentNode.children.length === 0) {
      return [];
    }
    
    // 返回子节点的副本
    return JSON.parse(JSON.stringify(parentNode.children));
  });
}