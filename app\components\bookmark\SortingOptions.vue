<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuContent } from '@/components/ui/dropdown-menu'

const props = defineProps<{
  sortBy: {
    field: 'default' | 'title' | 'dateAdded' | 'url' | 'id' | 'dateLastUsed';
    direction: 'asc' | 'desc';
  };
}>();

const emit = defineEmits(['sort']);

const sortOptions = [
  { value: 'default', label: i18n.t('options.sort.default') },
  { value: 'title', label: i18n.t('options.sort.title') },
  { value: 'dateAdded', label: i18n.t('options.sort.dateAdded') },
  { value: 'url', label: i18n.t('options.sort.url') },
  { value: 'id', label: i18n.t('options.sort.id') },
  { value: 'dateLastUsed', label: i18n.t('options.sort.dateLastUsed') },
];

function getSortDirectionIcon(field: string) {
  return props.sortBy.direction === 'asc' 
    ? 'lucide:arrow-up-down' 
    : 'lucide:arrow-down-up';
}
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="outline" size="sm" class="h-8">
        <Icon 
          :name="getSortDirectionIcon(sortBy.field)" 
          class="h-4 w-4"
        />
        {{ sortBy.field === 'default' ? 'Sort' : sortOptions.find(option => option.value === sortBy.field)?.label }}
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuItem 
        v-for="option in sortOptions" 
        :key="option.value"
        @click="emit('sort', option.value)"
      >
          <Icon 
            v-if="sortBy.field === option.value"
            :name="getSortDirectionIcon(option.value)" 
            class="h-4 w-4 text-muted-foreground"
          />
          <span v-else class="flex items-center h-4 w-4"></span>
        {{ option.label }}
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
