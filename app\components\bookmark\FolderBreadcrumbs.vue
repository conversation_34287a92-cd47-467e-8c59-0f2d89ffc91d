<script setup lang="ts">
import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'

import { get } from '~/utils/bookmark'

type BreadcrumbItem = {
  title: string;
  id: string;
}

const props = defineProps({
  bookmarkData: Array,
  folderId: String,
  maxCrumbs: {
    type: Number,
    default: 2
  }
});

const emit = defineEmits(['navigate']);

const breadcrumbs = ref<BreadcrumbItem[]>([])

// 获取面包屑节点
async function getBreadcrumbs() {
  let breadcrumbs = [];
  let currentId = props.folderId;

  // 循环查找父节点，直到没有 parentId 属性，即根节点
  while (currentId) {
    // 获取当前节点的数据
    const currentBookmark = await get(props.bookmarkData, currentId);
    // 如果当前节点不存在，或者为根节点，那么中断循环
    if (!currentBookmark || !currentBookmark.length || currentId === "0") {
      break;
    }
    // 将当前节点添加到面包屑数组
    breadcrumbs.unshift({ title: currentBookmark.title, id: currentId });
    // 更新当前 Id 为父节点的 Id
    currentId = currentBookmark[0].parentId;
  }

  return breadcrumbs;
}

async function setBreadcrumbs() {
  const start = performance.now();
  breadcrumbs.value = await getBreadcrumbs()
  const end = performance.now();
  // 实际上这个函数并不耗时间，时间长是因为列表渲染时间长，为什么有关联需要再看
  console.log(`breadcrumbs took ${(end - start).toFixed(0)} milliseconds`); // 40 ms
}

watchEffect(async () => {
  if (props.folderId) {
    setBreadcrumbs()
  }
});

const displayBreadcrumbs = computed(() => {
  if (breadcrumbs.value.length <= props.maxCrumbs) {
    return breadcrumbs.value;
  }

  // 保留最后 2 个面包屑，前面加省略号
  const lastCrumbs = breadcrumbs.value.slice(-props.maxCrumbs);
  return [{ title: '...', id: 'ellipsis' }, ...lastCrumbs];
});

// 处理面包屑点击
function handleCrumbClick(crumb: BreadcrumbItem) {
  if (crumb.id !== 'ellipsis' && crumb.id !== props.folderId) {
    emit('navigate', crumb.id);
  }
}
</script>

<template>
  <Breadcrumb>
    <BreadcrumbList :class="$attrs.class" class="gap-1 sm:gap-1 [&_svg]:size-2.5">
      <template v-for="(crumb, index) in displayBreadcrumbs" :key="index">
        <BreadcrumbItem class="gap-0">
          <BreadcrumbLink
            v-if="crumb.id !== 'ellipsis' && index < displayBreadcrumbs.length - 1"
            @click="handleCrumbClick(crumb)"
            class="text-muted-foreground hover:text-foreground cursor-pointer"
          >
            {{ crumb.title }}
          </BreadcrumbLink>
          <BreadcrumbPage
            v-else
            class="text-muted-foreground"
            :class="{ 'text-foreground': index === displayBreadcrumbs.length - 1 }"
          >
            {{ crumb.title }}
          </BreadcrumbPage>
        </BreadcrumbItem>
        <Icon v-if="index < displayBreadcrumbs.length - 1" name="radix-icons:slash" class="text-muted-foreground/70" />
      </template>
    </BreadcrumbList>
  </Breadcrumb>
</template>
