<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuCheckboxItem } from '@/components/ui/dropdown-menu'
import { Checkbox } from '@/components/ui/checkbox'
import { onMounted } from 'vue'
import { useVisibleFields } from '@/composables/useVisibleFields'

// 使用 composable 获取可见字段状态和方法
const { visibleFields, fieldList, loadVisibleFields, updateVisibleField, getFieldLabel } = useVisibleFields()

onMounted(() => {
  loadVisibleFields()
})
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button
        variant="outline"
        size="sm"
        class="ml-auto hidden h-8 lg:flex"
      >
        <Icon name="radix-icons:mixer-horizontal" />
        View
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent
      class="w-[150px]"
    >
      <DropdownMenuLabel>Toggle Columns</DropdownMenuLabel>
      <DropdownMenuSeparator />
      
      <DropdownMenuItem 
        v-for="field in fieldList"
        :key="field"
        @select.prevent="() => updateVisibleField(field, !visibleFields[field])"
      >
        <Checkbox 
          :model-value="visibleFields[field]"
          class="mr-2"
        />
        <span>{{ getFieldLabel(field) }}</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
